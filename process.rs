use crate::error::{Result, StabilizerError};
use sysinfo::{ProcessExt, System, SystemExt};

pub type ProcessId = sysinfo::Pid;

// The executable name for Universe Sandbox 2 might vary slightly.
// On Windows, it's likely "Universe Sandbox.exe". On macOS, "Universe Sandbox".
// The sysinfo `name()` method typically returns the name without the extension.
const TARGET_PROCESS_NAME: &str = "Universe Sandbox";

/// Finds the Process ID (PID) of the running Universe Sandbox 2 application.
/// This implementation is cross-platform thanks to the `sysinfo` crate.
pub fn find_process_id() -> Result<ProcessId> {
    println!("   -> Searching for '{}' process...", TARGET_PROCESS_NAME);

    let mut sys = System::new_all();
    sys.refresh_processes();

    let found_processes: Vec<_> = sys
        .processes_by_name(TARGET_PROCESS_NAME)
        .collect();

    match found_processes.len() {
        0 => Err(StabilizerError::ProcessNotFound),
        1 => {
            let process = found_processes[0];
            let pid = process.pid();
            println!("   -> Found process with PID: {}", pid);
            Ok(pid)
        }
        _ => {
            println!("   -> Warning: Multiple '{}' processes found. Using the first one found.");
            let process = found_processes[0];
            let pid = process.pid();
            println!("   -> Found process with PID: {}", pid);
            Ok(pid)
        }
    }
}