use thiserror::Error;

pub type Result<T> = std::result::Result<T, StabilizerError>;

#[derive(Erro<PERSON>, Debug)]
pub enum StabilizerError {
    #[error("I/O Error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Failed to parse JSON: {0}")]
    Json(#[from] serde_json::Error),

    #[error("Could not find a running Universe Sandbox 2 process.")]
    ProcessNotFound,

    #[error("Failed to attach to process (PID: {0}): {1}")]
    ProcessAttach(usize, String),

    #[error("Failed to read memory at address 0x{0:X}: {1}")]
    MemoryReadError(usize, String),

    #[error("Failed to write memory at address 0x{0:X}: {1}")]
    MemoryWriteError(usize, String),

    #[error("Physics solver encountered an issue: {0}")]
    SolverError(String),

    #[error("Physics solver failed to converge within {0} iterations.")]
    SolverDidNotConverge(u64),
}