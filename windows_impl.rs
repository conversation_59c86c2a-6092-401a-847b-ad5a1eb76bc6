use crate::error::{Result, StabilizerError};
use crate::process::ProcessId;
use std::mem::{size_of, MaybeUninit};
use winapi::shared::minwindef::{LPCVOID, LPVOID};
use winapi::um::errhandlingapi::GetLastError;
use winapi::um::handleapi::CloseHandle;
use winapi::um::memoryapi::{ReadProcessMemory, WriteProcessMemory};
use winapi::um::processthreadsapi::OpenProcess;
use winapi::um::winnt::{
    HANDLE, PROCESS_QUERY_INFORMATION, PROCESS_VM_OPERATION, PROCESS_VM_READ, PROCESS_VM_WRITE,
};

/// The Windows-specific implementation of a process handle.
/// It's a wrapper around the raw `HANDLE` type to ensure it's properly closed.
#[derive(Debug)]
pub struct ProcessHandleImpl {
    handle: HANDLE,
}

// Implement Drop to ensure the handle is closed when it goes out of scope.
// This is a critical part of safe resource management in Rust.
impl Drop for ProcessHandleImpl {
    fn drop(&mut self) {
        // The `unsafe` block is required because we are calling a C function.
        unsafe {
            CloseHandle(self.handle);
        }
    }
}

impl ProcessHandleImpl {
    /// Attaches to the target process by its PID.
    pub fn attach(pid: ProcessId) -> Result<Self> {
        // The required permissions to read from and write to the target process's memory.
        let desired_access =
            PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION | PROCESS_QUERY_INFORMATION;

        // OpenProcess returns a handle to the process.
        let handle = unsafe { OpenProcess(desired_access, 0, pid.as_u32()) };

        // A null handle indicates failure.
        if handle.is_null() {
            let error_code = unsafe { GetLastError() };
            let error_message = format!("WinAPI Error Code: {}", error_code);
            Err(StabilizerError::ProcessAttach(
                pid.as_u32() as usize,
                error_message,
            ))
        } else {
            println!("   -> Successfully attached to process.");
            Ok(ProcessHandleImpl { handle })
        }
    }

    /// Reads a typed data structure from a specific memory address.
    pub fn read<T: Sized + Copy>(&self, address: usize) -> Result<T> {
        let mut buffer: MaybeUninit<T> = MaybeUninit::uninit();
        let bytes_to_read = size_of::<T>();
        let mut bytes_read = 0;

        let success = unsafe {
            ReadProcessMemory(self.handle, address as LPCVOID, buffer.as_mut_ptr() as LPVOID, bytes_to_read, &mut bytes_read)
        };

        if success == 0 || bytes_read != bytes_to_read {
            let error_code = unsafe { GetLastError() };
            Err(StabilizerError::MemoryReadError(address, format!("WinAPI Error Code: {}", error_code)))
        } else {
            let data = unsafe { buffer.assume_init() };
            Ok(data)
        }
    }

    /// Writes a typed data structure to a specific memory address.
    pub fn write<T: Sized + Copy>(&self, address: usize, data: &T) -> Result<()> {
        let bytes_to_write = size_of::<T>();
        let mut bytes_written = 0;

        let success = unsafe {
            WriteProcessMemory(self.handle, address as LPVOID, data as *const T as LPCVOID, bytes_to_write, &mut bytes_written)
        };

        if success == 0 || bytes_written != bytes_to_write {
            let error_code = unsafe { GetLastError() };
            Err(StabilizerError::MemoryWriteError(address, format!("WinAPI Error Code: {}", error_code)))
        } else {
            Ok(())
        }
    }
}