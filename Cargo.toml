[package]
name = "stabilizer"
version = "2.1.0"
edition = "2021"
authors = ["<PERSON>. <PERSON> <<EMAIL>>"]
description = "A bespoke interface for refining n-body simulation initial conditions."

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
# For the Command-Line Interface (CLI)
clap = { version = "4.4", features = ["derive"] }

# For handling JSON goal and config files
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# For high-performance linear algebra (vectors, matrices, etc.)
nalgebra = "0.32"

# For multi-threading the physics calculations
rayon = "1.8"

# For logging and output
log = "0.4"

# For ergonomic error handling
thiserror = "1.0"

# For cross-platform process discovery
sysinfo = "0.29"

# Platform-specific dependencies for memory hooking
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["memoryapi", "processthreadsapi", "handleapi", "winnt"] }

[target.'cfg(target_os = "macos")'.dependencies]
mach = "0.3"