use crate::error::{Result, StabilizerError};
use serde::Deserialize;
use std::fs;
use std::path::Path;

/// Represents the configuration for the refinement calculation, from config.json.
#[derive(Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Config {
    pub time_horizon: String,
    pub precision_threshold: f64,
    pub max_iterations: u64,
    pub perturbation_sources: Vec<String>,
    pub output_file: String,
}

/// Represents a body in an orbital resonance goal.
#[derive(Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct ResonantBody {
    pub body_id: String,
    pub ratio: u32,
}

/// Represents the specific type of a narrative event.
#[derive(Deserialize, Debug, Clone)]
#[serde(tag = "event", rename_all = "PascalCase")]
pub enum NarrativeEventType {
    Eclipse {
        #[serde(rename = "observingBodyID")]
        observing_body_id: String,
        #[serde(rename = "targetBodyID")]
        target_body_id: String,
        #[serde(rename = "occludingBodyID")]
        occluding_body_id: String,
    },
    ClosePass {
        #[serde(rename = "primaryBodyID")]
        primary_body_id: String,
        #[serde(rename = "passingBodyID")]
        passing_body_id: String,
        #[serde(rename = "targetDistance")]
        target_distance: String,
    },
}

/// Represents the overall goal of the refinement, from goal.json.
#[derive(Deserialize, Debug, Clone)]
#[serde(tag = "goalType", rename_all = "PascalCase")]
pub enum Goal {
    OrbitalResonance {
        #[serde(rename = "primaryBodyID")]
        primary_body_id: String,
        #[serde(rename = "resonantBodies")]
        resonant_bodies: Vec<ResonantBody>,
        notes: String,
    },
    LongTermStability {
        #[serde(rename = "protectedBodies")]
        protected_bodies: Vec<String>,
        notes: String,
    },
    NarrativeEvent {
        #[serde(flatten)]
        event: NarrativeEventType,
        #[serde(rename = "targetTime")]
        target_time: String,
        notes: String,
    },
}

/// Loads the goal and config from their respective JSON files.
pub fn load_specifications(
    goal_path: &Path,
    config_path: &Path,
) -> Result<(Goal, Config)> {
    let goal_content = fs::read_to_string(goal_path)?;
    let goal_data: Goal = serde_json::from_str(&goal_content)?;

    let config_content = fs::read_to_string(config_path)?;
    let config_data: Config = serde_json::from_str(&config_content)?;

    Ok((goal_data, config_data))
}