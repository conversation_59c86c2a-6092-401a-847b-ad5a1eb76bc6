### **The Stabilizer: A Celestial Choreography Interface**

Technical Documentation \- Version 2.1 (for Universe Sandbox Build 35+)  
Author: Dr. <PERSON>

#### **1.0 Overview & Philosophy**

"The Stabilizer" is a bespoke software interface designed to bridge the gap between artistic intent and the rigorous physics of n-body simulation within Universe Sandbox 2\. It is not a tool for automated creation. Its purpose is not to generate systems from scratch, but to take an *existing*, artistically-composed but gravitationally imperfect system and refine its initial conditions to achieve long-term, stable, and predictable beauty.  
This tool operates on a single, core principle: **Refinement, not Generation.** The initial placement of celestial bodies—the broad, sweeping orbits and narrative-driven positions—is the work of the artist. The Stabilizer acts as a master artisan, performing the thousands of microscopic calculations necessary to ensure the artist's vision can endure for simulated eons without succumbing to gravitational chaos. It translates a high-level, often narrative goal into the precise velocity vectors required to realize it.

#### **2.0 Prerequisites & System Integration**

**2.1 Software Requirements:**

* **Primary Application:** Universe Sandbox 2 (Build 35.0 or later is required to leverage the updated graphics engine and DOTS-based physics).  
* **Operating System:** Windows 10 (21H1+) or macOS 11.0+ (Apple Silicon recommended).

2.2 Hardware & Driver Integration:  
The Stabilizer is designed to be used as part of "The Podium" control rig. While not strictly required for the software to run, the intended workflow necessitates:

* **3Dconnexion SpaceMouse:** Requires official 3Dconnexion drivers (latest version) for fluid 6-axis viewport navigation.  
* **Wacom-series Graphics Tablet:** Requires official Wacom drivers for pressure-sensitive input, used for the initial "drawing" of orbital paths.  
* **Custom Control Dials:** Interfaced via a custom serial port driver (documentation not public).

2.3 Integration Method:  
The Stabilizer is a standalone executable that interfaces with Universe Sandbox 2 via a direct memory-hooking mechanism. It reads the current simulation state, performs its calculations externally, and then writes the refined state back into the simulation's memory. Warning: This method is inherently invasive and is not a supported feature of Universe Sandbox 2\. Improper use can lead to simulation instability or crashes.

#### **3.0 Core Interface Function**

The Stabilizer has a single primary function, exposed through its command-line interface.  
refineSystemState(goal, config)  
This function initiates the refinement process. It automatically captures the current state of the active Universe Sandbox 2 simulation.  
**Parameters:**

* goal (JSON object): A structured object defining the desired long-term behavior of the system. This is the core of the refinement request. See section 4.0 for Goal Structures.  
* config (JSON object): A configuration object that specifies the parameters for the calculation itself. See section 5.0 for Configuration Structure.

**Returns:**  
A RefinedSystemState JSON object containing the list of celestial bodies with their new, precisely calculated velocity vectors. The function also outputs a .sim file that can be directly loaded into Universe Sandbox 2\.

#### **4.0 Goal Structures**

The goal parameter defines the artistic and physical intent. It must conform to one of the following structures.  
4.1 OrbitalResonanceGoal  
To establish a stable, long-term orbital resonance between two or more bodies.  
{  
  "goalType": "OrbitalResonance",  
  "primaryBodyID": "Sun",  
  "resonantBodies": \[  
    {  
      "bodyID": "PlanetA",  
      "ratio": 2  
    },  
    {  
      "bodyID": "PlanetB",  
      "ratio": 5  
    }  
  \],  
  "notes": "Goal is to establish a stable 2:5 resonance between PlanetA and PlanetB."  
}

4.2 LongTermStabilityGoal  
To ensure the overall stability of a system, preventing ejections or chaotic collisions over a long period.  
{  
  "goalType": "LongTermStability",  
  "protectedBodies": \["Earth", "Moon"\],  
  "notes": "Ensure the Earth-Moon system remains stable and within the habitable zone for the specified time horizon."  
}

4.3 NarrativeEventGoal  
To choreograph a specific, timed event, such as a transit, eclipse, or close pass.  
{  
  "goalType": "NarrativeEvent",  
  "event": "Eclipse",  
  "observingBodyID": "Earth",  
  "targetBodyID": "Sun",  
  "occludingBodyID": "Moon",  
  "targetTime": "500y",  
  "notes": "Choreograph a perfect total solar eclipse visible from Earth's primary continent in 500 years."  
}

#### **5.0 Configuration Structure**

The config parameter controls the behavior of the refinement calculation.  
{  
  "timeHorizon": "10000y",  
  "precisionThreshold": 0.001,  
  "maxIterations": 50000,  
  "perturbationSources": \["ALL"\],  
  "outputFile": "MyMasterpiece\_refined.sim"  
}

* timeHorizon (string): The duration over which the goal's stability should be calculated (e.g., "100y", "10000y").  
* precisionThreshold (float): The maximum acceptable deviation from the goal state over the time horizon, as a percentage. A value of 0.001 means the final orbits must not deviate by more than 0.001%.  
* maxIterations (integer): The maximum number of computational iterations the solver will perform.  
* perturbationSources (array of strings): Specifies which bodies to consider as major sources of gravitational perturbation. Can be a list of Body IDs or \["ALL"\].  
* outputFile (string): The name of the resulting simulation file.

#### **6.0 Example Workflow: "The Sculptor's Tear"**

This example outlines the process for creating the initial phase of "The Sculptor's Tear," where a small moon is placed on a decaying orbit to gravitationally sculpt a molten planet.

1. **Artistic Placement:** In Universe Sandbox 2, a star is placed. A large, molten proto-planet is placed in a stable circular orbit. A smaller, dense moon is then placed in a slightly wider, slightly elliptical orbit using the graphics tablet, giving it an initial vector that will cause it to slowly spiral inward.  
2. **Define the Goal & Config:** Two JSON files are created.  
   * sculptor\_goal.json:  
     {  
       "goalType": "NarrativeEvent",  
       "event": "ClosePass",  
       "primaryBodyID": "ProtoPlanet",  
       "passingBodyID": "SculptorMoon",  
       "targetDistance": "1.2x RocheLimit",  
       "targetTime": "1.2My",  
       "notes": "Ensure the SculptorMoon makes a close pass just outside the Roche Limit of the ProtoPlanet at the 1.2 million year mark, maximizing tidal forces without causing immediate breakup."  
     }

   * sculptor\_config.json:  
     {  
       "timeHorizon": "2My",  
       "precisionThreshold": 0.01,  
       "maxIterations": 100000,  
       "perturbationSources": \["Star", "ProtoPlanet", "SculptorMoon"\],  
       "outputFile": "SculptorsTear\_v1.sim"  
     }

3. Execute Refinement: The following command is run from the terminal:  
   ./stabilizer refineSystemState \--goal=sculptor\_goal.json \--config=sculptor\_config.json  
4. **Apply and Observe:** The Stabilizer performs its calculations and generates SculptorsTear\_v1.sim. This file is loaded in Universe Sandbox 2\. The simulation is then run at high speed to observe the long-term decay of the moon's orbit, ensuring it performs its gravitational sculpting as intended. Minor adjustments to the planet's surface material properties may be made manually with the control dials during this observation phase.

#### **7.0 A Note from the Choreographer**

This tool is an extension of my artistic process. It is complex, unforgiving, and requires a deep understanding of both orbital mechanics and aesthetic composition. It is not, and will never be, made publicly available. This documentation serves not as an invitation, but as a record of a methodology—a glimpse into the quiet, patient work of conducting the music of the spheres.